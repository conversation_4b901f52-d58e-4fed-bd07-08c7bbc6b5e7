# Leaflet 地图使用指南

本指南介绍如何在 Markdown 文档中轻松嵌入基于 OpenStreetMap 和 Leaflet 的交互式地图。

## 功能特性

- ✅ 基于开源的 OpenStreetMap 数据
- ✅ 使用 Leaflet 提供交互式地图体验
- ✅ 支持多种地图配置选项
- ✅ 支持自定义标记点和弹窗
- ✅ 响应式设计，适配各种屏幕尺寸
- ✅ 在 Markdown 中使用简单的指令语法
- ✅ 支持 SSR（服务端渲染）

## 基本用法

### 1. 简单地图

最简单的用法是指定经纬度坐标：

```markdown
:::map{lat=39.9042 lng=116.4074}
:::
```

这将显示一个以北京天安门为中心的地图。

### 2. 自定义缩放级别和尺寸

```markdown
:::map{lat=31.2304 lng=121.4737 zoom=15 height="500px"}
:::
```

### 3. 带标记点的地图

```markdown
:::map{lat=39.9042 lng=116.4074 zoom=12}
:::marker{lat=39.9042 lng=116.4074 title="天安门"}
这里是中华人民共和国的象征性建筑。
:::

:::marker{lat=39.9163 lng=116.3972 title="故宫"}
明清两朝的皇家宫殿，现为故宫博物院。
:::
:::
```

### 4. 使用 JSON 格式的标记点

```markdown
:::map{lat=39.9042 lng=116.4074 zoom=12 markers='[{"lat":39.9042,"lng":116.4074,"popup":"天安门","title":"天安门"},{"lat":39.9163,"lng":116.3972,"popup":"故宫博物院","title":"故宫"}]'}
:::
```

### 5. 简化语法（叶子指令）

对于简单的单点地图，可以使用更简洁的语法：

```markdown
:map[lat=39.9042 lng=116.4074 zoom=15]
```

## 配置选项

### 基本参数

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `lat` / `latitude` | number | 必需 | 纬度坐标 |
| `lng` / `longitude` | number | 必需 | 经度坐标 |
| `zoom` | number | 13 | 地图缩放级别 (1-19) |
| `height` | string | "400px" | 地图高度 |
| `width` | string | "100%" | 地图宽度 |
| `class` / `className` | string | "" | 自定义 CSS 类名 |

### 高级参数

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `tileLayer` | string | OpenStreetMap | 自定义瓦片图层 URL |
| `attribution` | string | OSM 版权信息 | 自定义版权归属信息 |
| `markers` | JSON string | [] | 标记点数组 |

### 标记点格式

每个标记点支持以下属性：

```json
{
  "lat": 39.9042,      // 纬度（必需）
  "lng": 116.4074,     // 经度（必需）
  "popup": "弹窗内容",   // 点击标记时显示的弹窗内容
  "title": "提示文本"    // 鼠标悬停时显示的提示文本
}
```

## 实际示例

### 示例 1：旅游景点地图

```markdown
:::map{lat=39.9042 lng=116.4074 zoom=11 height="600px"}
:::marker{lat=39.9042 lng=116.4074 title="天安门广场"}
中华人民共和国的象征，世界上最大的城市广场之一。

**开放时间：** 全天开放  
**门票：** 免费
:::

:::marker{lat=39.9163 lng=116.3972 title="故宫博物院"}
明清两朝的皇家宫殿，收藏了大量珍贵文物。

**开放时间：** 8:30-17:00  
**门票：** 60元（淡季）/ 80元（旺季）
:::

:::marker{lat=39.8889 lng=116.3975 title="天坛公园"}
明清皇帝祭天的场所，建筑精美，环境优雅。

**开放时间：** 6:00-22:00  
**门票：** 15元
:::
:::
```

### 示例 2：自定义样式地图

```markdown
:::map{lat=31.2304 lng=121.4737 zoom=13 height="400px" class="custom-map"}
:::marker{lat=31.2304 lng=121.4737 title="上海外滩"}
上海的标志性景观，可以欣赏黄浦江两岸的美景。
:::
:::
```

## 技术实现

### 组件架构

- **LeafletMap.astro**: 主要的地图组件，负责渲染交互式地图
- **remark-map.ts**: Remark 插件，解析 Markdown 中的地图指令
- **动态加载**: 使用动态导入避免 SSR 问题

### 样式定制

地图组件使用了响应式设计和现代 CSS，你可以通过以下方式自定义样式：

```css
/* 自定义地图容器样式 */
.custom-map {
  border: 2px solid #3b82f6;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* 自定义加载状态 */
.custom-map .map-loading {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}
```

## 常见问题

### Q: 地图不显示怎么办？

A: 请检查：
1. 经纬度坐标是否正确
2. 网络连接是否正常
3. 浏览器控制台是否有错误信息

### Q: 如何获取经纬度坐标？

A: 你可以：
1. 使用 Google Maps，右键点击位置查看坐标
2. 使用在线坐标拾取工具
3. 使用 GPS 设备或手机应用

### Q: 可以使用其他地图服务吗？

A: 可以通过 `tileLayer` 参数使用其他兼容 Leaflet 的瓦片服务，例如：
- Mapbox
- CartoDB
- Stamen Maps

### Q: 如何优化地图加载性能？

A: 地图组件已经实现了：
1. 动态加载 Leaflet 库
2. 延迟初始化
3. CSS 样式缓存

## 更新日志

- **v1.0.0**: 初始版本，支持基本地图功能和标记点
- 支持容器指令和叶子指令语法
- 集成 OpenStreetMap 和 Leaflet
- 响应式设计和 SSR 支持
