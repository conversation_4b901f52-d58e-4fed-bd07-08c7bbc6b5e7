{"name": "moatkon-blog", "type": "module", "version": "6.9.0", "private": false, "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro build", "postbuild": "pagefind --site dist", "preview": "astro preview", "lint": "biome lint .", "format": "pnpm run format:code && pnpm run format:imports", "format:code": "biome format . --write && prettier -w \"**/*\" \"!**/*.{md,mdx}\" --ignore-unknown --cache", "format:imports": "biome check --formatter-enabled=false --write", "check": "astro check"}, "dependencies": {"@astrojs/markdown-remark": "^6.3.6", "@astrojs/mdx": "4.3.5", "@astrojs/rss": "4.0.12", "@astrojs/sitemap": "3.5.1", "@tailwindcss/vite": "4.1.13", "@types/leaflet": "^1.9.20", "astro": "5.13.7", "astro-expressive-code": "^0.41.3", "astro-icon": "^1.1.5", "astro-robots-txt": "^1.0.0", "astro-webmanifest": "^1.0.0", "cssnano": "^7.1.1", "hastscript": "^9.0.0", "leaflet": "^1.9.4", "mdast-util-directive": "^3.0.0", "mdast-util-to-markdown": "^2.1.2", "mdast-util-to-string": "^4.0.0", "rehype-autolink-headings": "^7.1.0", "rehype-external-links": "^3.0.0", "rehype-unwrap-images": "^1.0.0", "remark-directive": "^4.0.0", "satori": "0.18.2", "satori-html": "^0.3.2", "sharp": "^0.34.3", "unified": "^11.0.5", "unist-util-visit": "^5.0.0"}, "devDependencies": {"@astrojs/check": "^0.9.4", "@biomejs/biome": "^2.2.4", "@iconify-json/mdi": "^1.2.2", "@pagefind/default-ui": "^1.4.0", "@resvg/resvg-js": "^2.6.2", "@tailwindcss/typography": "^0.5.16", "@types/hast": "^3.0.4", "@types/mdast": "^4.0.4", "autoprefixer": "^10.4.21", "pagefind": "^1.4.0", "prettier": "^3.6.2", "prettier-plugin-astro": "0.14.1", "prettier-plugin-tailwindcss": "^0.6.14", "reading-time": "^1.5.0", "tailwindcss": "4.1.13", "typescript": "^5.9.2"}, "pnpm": {"overrides": {"sharp": "^0.34.2"}, "onlyBuiltDependencies": ["@biomejs/biome", "@tailwindcss/oxide", "esbuild", "sharp"]}}