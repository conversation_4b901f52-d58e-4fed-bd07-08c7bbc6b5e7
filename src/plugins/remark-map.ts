import type { Plugin } from "unified";
import type { Root } from "mdast";
import { visit } from "unist-util-visit";
import { h } from "hastscript";

interface MapDirective {
  type: "containerDirective" | "leafDirective";
  name: string;
  attributes?: Record<string, string>;
  children?: any[];
}

interface MapConfig {
  lat: number;
  lng: number;
  zoom?: number;
  height?: string;
  width?: string;
  markers?: Array<{
    lat: number;
    lng: number;
    popup?: string;
    title?: string;
  }>;
  tileLayer?: string;
  attribution?: string;
  className?: string;
}

/**
 * 解析地图配置参数
 */
function parseMapConfig(attributes: Record<string, string> = {}): MapConfig | null {
  const lat = parseFloat(attributes.lat || attributes.latitude || "");
  const lng = parseFloat(attributes.lng || attributes.longitude || "");

  if (isNaN(lat) || isNaN(lng)) {
    console.warn("Map directive missing required lat/lng coordinates");
    return null;
  }

  const config: MapConfig = {
    lat,
    lng,
    zoom: attributes.zoom ? parseInt(attributes.zoom) : 13,
    height: attributes.height || "400px",
    width: attributes.width || "100%",
    className: attributes.class || attributes.className || "",
  };

  // 解析标记点
  if (attributes.markers) {
    try {
      config.markers = JSON.parse(attributes.markers);
    } catch (error) {
      console.warn("Invalid markers JSON in map directive:", error);
    }
  }

  // 自定义瓦片图层
  if (attributes.tileLayer) {
    config.tileLayer = attributes.tileLayer;
  }

  // 自定义归属信息
  if (attributes.attribution) {
    config.attribution = attributes.attribution;
  }

  return config;
}

/**
 * 解析标记点从指令内容
 */
function parseMarkersFromContent(children: any[]): Array<{
  lat: number;
  lng: number;
  popup?: string;
  title?: string;
}> {
  const markers: Array<{
    lat: number;
    lng: number;
    popup?: string;
    title?: string;
  }> = [];

  // 遍历子节点寻找标记点定义
  visit({ type: "root", children }, "containerDirective", (node: any) => {
    if (node.name === "marker") {
      const lat = parseFloat(node.attributes?.lat || "");
      const lng = parseFloat(node.attributes?.lng || "");
      
      if (!isNaN(lat) && !isNaN(lng)) {
        const marker: any = { lat, lng };
        
        if (node.attributes?.title) {
          marker.title = node.attributes.title;
        }
        
        // 提取弹窗内容
        if (node.children && node.children.length > 0) {
          // 简单地将文本内容作为弹窗内容
          const textContent = node.children
            .filter((child: any) => child.type === "paragraph")
            .map((child: any) => 
              child.children
                .filter((c: any) => c.type === "text")
                .map((c: any) => c.value)
                .join("")
            )
            .join("\n");
          
          if (textContent) {
            marker.popup = textContent;
          }
        }
        
        markers.push(marker);
      }
    }
  });

  return markers;
}

/**
 * Remark 插件：将地图指令转换为地图组件
 */
export const remarkMap: Plugin<[], Root> = () => {
  return (tree) => {
    visit(tree, "containerDirective", (node: MapDirective, index, parent) => {
      if (node.name !== "map") return;

      const config = parseMapConfig(node.attributes);
      if (!config) return;

      // 从指令内容中解析标记点
      if (node.children && node.children.length > 0) {
        const contentMarkers = parseMarkersFromContent(node.children);
        if (contentMarkers.length > 0) {
          config.markers = contentMarkers;
        }
      }

      // 创建地图组件的 props
      const props: Record<string, any> = {
        latitude: config.lat,
        longitude: config.lng,
        zoom: config.zoom,
        height: config.height,
        width: config.width,
      };

      if (config.markers && config.markers.length > 0) {
        props.markers = config.markers;
      }

      if (config.tileLayer) {
        props.tileLayer = config.tileLayer;
      }

      if (config.attribution) {
        props.attribution = config.attribution;
      }

      if (config.className) {
        props.className = config.className;
      }

      // 替换节点为地图组件
      const mapComponent = {
        type: "mdxJsxFlowElement",
        name: "LeafletMap",
        attributes: Object.entries(props).map(([key, value]) => ({
          type: "mdxJsxAttribute",
          name: key,
          value: typeof value === "string" ? value : JSON.stringify(value),
        })),
        children: [],
      };

      if (parent && typeof index === "number") {
        parent.children[index] = mapComponent;
      }
    });

    // 也支持简单的叶子指令格式 :map[lat=xxx lng=xxx]
    visit(tree, "leafDirective", (node: MapDirective, index, parent) => {
      if (node.name !== "map") return;

      const config = parseMapConfig(node.attributes);
      if (!config) return;

      const props: Record<string, any> = {
        latitude: config.lat,
        longitude: config.lng,
        zoom: config.zoom,
        height: config.height,
        width: config.width,
      };

      if (config.className) {
        props.className = config.className;
      }

      const mapComponent = {
        type: "mdxJsxFlowElement",
        name: "LeafletMap",
        attributes: Object.entries(props).map(([key, value]) => ({
          type: "mdxJsxAttribute",
          name: key,
          value: typeof value === "string" ? value : JSON.stringify(value),
        })),
        children: [],
      };

      if (parent && typeof index === "number") {
        parent.children[index] = mapComponent;
      }
    });
  };
};
