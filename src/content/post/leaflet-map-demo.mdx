---
title: "OpenStreetMap + Leaflet 地图演示"
description: "展示如何在 Markdown 中轻松嵌入交互式地图"
publishDate: "2025-09-25"
tags: ["地图", "OpenStreetMap", "Leaflet", "教程"]
draft: false
---

# OpenStreetMap + Leaflet 地图演示

这篇文章演示了如何在 Markdown 中使用基于 OpenStreetMap 和 Leaflet 的开源地图解决方案。

## 基本地图示例

下面是一个简单的地图，显示北京天安门广场：

:::map{lat=39.9042 lng=116.4074 zoom=13}
:::

## 带标记点的地图

这个地图展示了北京的几个著名景点：

:::map{lat=39.9042 lng=116.4074 zoom=12 height="500px"}
:::marker{lat=39.9042 lng=116.4074 title="天安门广场"}
中华人民共和国的象征性建筑，世界上最大的城市广场之一。

**特色：**
- 面积达44万平方米
- 可容纳100万人集会
- 周围有人民大会堂、国家博物馆等重要建筑
:::

:::marker{lat=39.9163 lng=116.3972 title="故宫博物院"}
明清两朝的皇家宫殿，现为世界上最大的古代宫殿建筑群。

**亮点：**
- 有房屋9999.5间
- 收藏文物超过180万件
- 1987年被列为世界文化遗产
:::

:::marker{lat=39.8889 lng=116.3975 title="天坛公园"}
明清皇帝祭天祈谷的场所，中国古代建筑的杰作。

**建筑特色：**
- 祈年殿：三重檐圆形大殿
- 回音壁：奇妙的声学现象
- 圜丘坛：皇帝祭天的神圣场所
:::
:::

## 上海外滩地图

让我们看看上海的标志性景观：

:::map{lat=31.2304 lng=121.4737 zoom=15 height="400px"}
:::marker{lat=31.2304 lng=121.4737 title="外滩"}
上海最著名的景观大道，可以欣赏黄浦江两岸的美景。

这里有：
- 52幢风格各异的古典建筑
- 浦东陆家嘴金融区的现代天际线
- 黄浦江游船码头
:::
:::

## 简化语法示例

对于简单的单点地图，可以使用更简洁的语法：

:map[lat=22.3193 lng=114.1694 zoom=11]

这显示了香港的地图。

## 自定义尺寸的地图

你可以自定义地图的尺寸：

:::map{lat=25.0330 lng=121.5654 zoom=12 height="300px" width="80%"}
:::marker{lat=25.0330 lng=121.5654 title="台北101"}
曾经的世界第一高楼，台北的地标建筑。
:::
:::

## 技术特性

这个地图解决方案具有以下特性：

### ✅ 开源免费
- 基于 OpenStreetMap 开放数据
- 使用 Leaflet 开源地图库
- 无需 API 密钥或付费服务

### ✅ 功能丰富
- 交互式缩放和平移
- 自定义标记点和弹窗
- 响应式设计
- 支持多种地图样式

### ✅ 易于使用
- 简单的 Markdown 指令语法
- 支持嵌套标记点定义
- 灵活的配置选项

### ✅ 性能优化
- 动态加载，避免 SSR 问题
- 延迟初始化
- 轻量级实现

## 使用方法

在你的 Markdown 文件中，只需要使用以下语法：

```markdown
:::map{lat=纬度 lng=经度 zoom=缩放级别}
:::marker{lat=纬度 lng=经度 title="标题"}
标记点的描述内容
:::
:::
```

就这么简单！

## 更多示例

### 世界著名城市

让我们看看巴黎的埃菲尔铁塔：

:::map{lat=48.8584 lng=2.2945 zoom=16}
:::marker{lat=48.8584 lng=2.2945 title="埃菲尔铁塔"}
巴黎的象征，建于1889年，高324米。
:::
:::

### 自然景观

美国大峡谷国家公园：

:::map{lat=36.1069 lng=-112.1129 zoom=10}
:::marker{lat=36.1069 lng=-112.1129 title="大峡谷南缘"}
世界七大自然奇观之一，科罗拉多河切割形成的壮观峡谷。
:::
:::

## 总结

通过这个基于 OpenStreetMap 和 Leaflet 的解决方案，你可以：

1. **轻松嵌入地图**：使用简单的 Markdown 指令
2. **完全开源**：无需担心 API 限制或费用
3. **功能完整**：支持标记点、弹窗、自定义样式等
4. **性能优秀**：动态加载，响应式设计

这为博客、文档和其他内容网站提供了一个完美的地图解决方案！
