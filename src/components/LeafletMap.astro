---
export interface Props {
  id?: string;
  latitude: number;
  longitude: number;
  zoom?: number;
  height?: string;
  width?: string;
  markers?: Array<{
    lat: number;
    lng: number;
    popup?: string;
    title?: string;
  }>;
  tileLayer?: string;
  attribution?: string;
  className?: string;
}

const {
  id = `map-${Math.random().toString(36).substr(2, 9)}`,
  latitude,
  longitude,
  zoom = 13,
  height = "400px",
  width = "100%",
  markers = [],
  tileLayer = "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
  attribution = '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
  className = "",
} = Astro.props;

// 添加默认标记点（如果没有提供标记点的话）
const allMarkers = markers.length > 0 ? markers : [{ lat: latitude, lng: longitude }];
---

<div 
  id={id} 
  class={`leaflet-map ${className}`}
  style={`height: ${height}; width: ${width};`}
  data-lat={latitude}
  data-lng={longitude}
  data-zoom={zoom}
  data-tile-layer={tileLayer}
  data-attribution={attribution}
  data-markers={JSON.stringify(allMarkers)}
>
  <div class="map-loading">
    <div class="loading-spinner"></div>
    <p>正在加载地图...</p>
  </div>
</div>

<style>
  .leaflet-map {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    margin: 1rem 0;
  }

  .map-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background-color: #f3f4f6;
    color: #6b7280;
  }

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 0.5rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* 确保 Leaflet 样式正确加载 */
  :global(.leaflet-container) {
    font-family: inherit;
  }

  :global(.leaflet-popup-content-wrapper) {
    border-radius: 6px;
  }

  :global(.leaflet-popup-tip) {
    background: white;
  }
</style>

<script>
  // 动态导入 Leaflet 以避免 SSR 问题
  async function initializeMap(mapElement: HTMLElement) {
    try {
      // 动态导入 Leaflet
      const L = await import('leaflet');
      
      // 导入 Leaflet CSS
      if (!document.querySelector('link[href*="leaflet.css"]')) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
        link.integrity = 'sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=';
        link.crossOrigin = '';
        document.head.appendChild(link);
      }

      // 获取地图配置
      const lat = parseFloat(mapElement.dataset.lat!);
      const lng = parseFloat(mapElement.dataset.lng!);
      const zoom = parseInt(mapElement.dataset.zoom!);
      const tileLayer = mapElement.dataset.tileLayer!;
      const attribution = mapElement.dataset.attribution!;
      const markers = JSON.parse(mapElement.dataset.markers!);

      // 清除加载提示
      mapElement.innerHTML = '';

      // 创建地图
      const map = L.map(mapElement).setView([lat, lng], zoom);

      // 添加瓦片图层
      L.tileLayer(tileLayer, {
        attribution: attribution,
        maxZoom: 19,
      }).addTo(map);

      // 添加标记点
      markers.forEach((marker: any) => {
        const leafletMarker = L.marker([marker.lat, marker.lng]).addTo(map);
        
        if (marker.popup) {
          leafletMarker.bindPopup(marker.popup);
        }
        
        if (marker.title) {
          leafletMarker.bindTooltip(marker.title);
        }
      });

      // 如果有多个标记点，调整视图以包含所有标记点
      if (markers.length > 1) {
        const group = new L.featureGroup(
          markers.map((marker: any) => L.marker([marker.lat, marker.lng]))
        );
        map.fitBounds(group.getBounds().pad(0.1));
      }

    } catch (error) {
      console.error('Failed to initialize map:', error);
      mapElement.innerHTML = `
        <div class="map-error">
          <p>地图加载失败，请刷新页面重试。</p>
        </div>
      `;
    }
  }

  // 初始化所有地图
  function initializeMaps() {
    const mapElements = document.querySelectorAll('.leaflet-map');
    mapElements.forEach((element) => {
      if (element instanceof HTMLElement && !element.classList.contains('map-initialized')) {
        element.classList.add('map-initialized');
        initializeMap(element);
      }
    });
  }

  // 页面加载完成后初始化地图
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeMaps);
  } else {
    initializeMaps();
  }

  // 支持动态内容（如果页面内容是动态加载的）
  document.addEventListener('astro:page-load', initializeMaps);
</script>
