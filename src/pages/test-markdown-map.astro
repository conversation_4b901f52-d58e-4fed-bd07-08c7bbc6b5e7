---
import BaseLayout from "@/layouts/Base.astro";
---

<BaseLayout
  meta={{
    title: "Markdown地图测试",
    description: "测试Markdown中的地图功能",
  }}
>
  <main class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-8">Markdown地图测试</h1>

    <div class="prose max-w-none">
      <h2>测试1: 静态地图</h2>
      <p>这是一个静态地图测试：</p>

      <div
        class="markdown-map-container"
        id="test-map-1"
        data-map-config='{"type":"static","address":"北京天安门","height":"300px","width":"100%","zoom":15,"markerColor":"red","showMarker":true}'
        style="width: 100%; height: 300px; position: relative; background: #f3f4f6; border-radius: 8px; overflow: hidden;"
      >
        <div
          style="display: flex; align-items: center; justify-content: center; height: 100%; color: #6b7280; font-size: 14px;"
        >
          <div style="text-align: center;">
            <div style="margin-bottom: 8px;">📍</div>
            <div>地图加载中...</div>
            <button
              onclick="loadInteractiveMap('test-map-1')"
              style="margin-top: 8px; padding: 4px 12px; background: #3b82f6; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;"
            >
              加载地图
            </button>
          </div>
        </div>
      </div>

      <h2>测试2: Leaflet交互式地图</h2>
      <p>这是一个交互式地图测试：</p>

      <div
        class="markdown-map-container"
        id="test-map-2"
        data-map-config='{"type":"leaflet","address":"上海外滩","height":"400px","width":"100%","zoom":15,"theme":"default","showMarker":true}'
        style="width: 100%; height: 400px; position: relative; background: #f3f4f6; border-radius: 8px; overflow: hidden;"
      >
        <div
          style="display: flex; align-items: center; justify-content: center; height: 100%; color: #6b7280; font-size: 14px;"
        >
          <div style="text-align: center;">
            <div style="margin-bottom: 8px;">📍</div>
            <div>地图加载中...</div>
            <button
              onclick="loadInteractiveMap('test-map-2')"
              style="margin-top: 8px; padding: 4px 12px; background: #3b82f6; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;"
            >
              加载交互式地图
            </button>
          </div>
        </div>
      </div>

      <h2>测试3: Google嵌入地图</h2>
      <p>这是一个Google嵌入地图测试：</p>

      <div
        class="markdown-map-container"
        id="test-map-3"
        data-map-config='{"type":"embed","address":"杭州西湖","height":"350px","width":"100%","zoom":15}'
        style="width: 100%; height: 350px; position: relative; background: #f3f4f6; border-radius: 8px; overflow: hidden;"
      >
        <div
          style="display: flex; align-items: center; justify-content: center; height: 100%; color: #6b7280; font-size: 14px;"
        >
          <div style="text-align: center;">
            <div style="margin-bottom: 8px;">📍</div>
            <div>地图加载中...</div>
            <button
              onclick="loadInteractiveMap('test-map-3')"
              style="margin-top: 8px; padding: 4px 12px; background: #3b82f6; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;"
            >
              加载Google地图
            </button>
          </div>
        </div>
      </div>

      <h2>测试4: 使用坐标的地图</h2>
      <p>这是一个使用坐标的地图测试：</p>

      <div
        class="markdown-map-container"
        id="test-map-4"
        data-map-config='{"type":"static","lat":39.9042,"lng":116.4074,"height":"300px","width":"100%","zoom":16,"markerColor":"blue","markerTitle":"天安门广场"}'
        style="width: 100%; height: 300px; position: relative; background: #f3f4f6; border-radius: 8px; overflow: hidden;"
      >
        <div
          style="display: flex; align-items: center; justify-content: center; height: 100%; color: #6b7280; font-size: 14px;"
        >
          <div style="text-align: center;">
            <div style="margin-bottom: 8px;">📍</div>
            <div>地图加载中...</div>
            <button
              onclick="loadInteractiveMap('test-map-4')"
              style="margin-top: 8px; padding: 4px 12px; background: #3b82f6; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;"
            >
              加载坐标地图
            </button>
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- 地图加载脚本 -->
  <script src="/markdown-map-loader.js" is:inline></script>

  <!-- 地图样式 -->
  <style>
    @import "/src/styles/map.css";

    /* Markdown地图容器样式 */
    .markdown-map-container {
      margin: 1.5rem 0;
      border-radius: 8px;
      overflow: hidden;
      box-shadow:
        0 4px 6px -1px rgb(0 0 0 / 0.1),
        0 2px 4px -2px rgb(0 0 0 / 0.1);
      background-color: #f3f4f6;
    }

    @media (prefers-color-scheme: dark) {
      .markdown-map-container {
        background-color: #374151;
      }
    }
  </style>
</BaseLayout>
